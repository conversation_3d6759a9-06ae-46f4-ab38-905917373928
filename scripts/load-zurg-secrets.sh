#! /bin/bash

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"

# Load environment variables
source "${PROJECT_ROOT}/.env"

# Export variables for envsubst
export ZURG_RD_TOKEN
export ZURG_PROXY
export ZURG_PLEX_TOKEN

# Load Zurg secrets
envsubst < "${PROJECT_ROOT}/configs/zurg.yml.template" > "${PROJECT_ROOT}/configs/zurg.yml"

# Verify the secrets were loaded correctly
CONFIG_FILE="${PROJECT_ROOT}/configs/zurg.yml"
if [ ! -f "$CONFIG_FILE" ]; then
  echo "Error: Failed to create zurg.yml"
  exit 1
fi

# Check if values were properly substituted
if grep -q "token: $ZURG_RD_TOKEN" "$CONFIG_FILE" && 
   grep -q "proxy: \"$ZURG_PROXY\"" "$CONFIG_FILE" && 
   grep -q "plex_token: \"$ZURG_PLEX_TOKEN\"" "$CONFIG_FILE"; then
  echo "✅ Zurg secrets loaded and verified successfully."
else
  echo "❌ Error: Some environment variables were not properly substituted."
  exit 1
fi
