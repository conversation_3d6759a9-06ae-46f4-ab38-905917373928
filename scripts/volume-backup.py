#!/usr/bin/env python3

import os
import subprocess
import datetime
import sys
import boto3
import json
import argparse # Re-add argparse
import shutil # Add shutil for directory cleanup
from botocore.exceptions import ClientError
from dotenv import load_dotenv

# Environment variable for specifying volumes (comma-separated)
BACKUP_VOLUMES_ENV_VAR = "BACKUP_VOLUMES"

# Default backup directory
DEFAULT_BACKUP_DIR = "/tmp/backups"

# Load environment variables from .env file relative to the script's location or project root
# Assumes .env is in the parent directory of 'scripts'
dotenv_path = os.path.join(os.path.dirname(__file__), '..', '.env')
if os.path.exists(dotenv_path):
    load_dotenv(dotenv_path=dotenv_path)
else:
    # Fallback to loading .env from the current working directory if not found relative to script
    load_dotenv()


# S3 configuration from environment variables
S3_BUCKET = os.getenv("S3_BUCKET_NAME")
S3_ENDPOINT_URL = os.getenv("S3_ENDPOINT_URL") # e.g., https://<accountid>.r2.cloudflarestorage.com or https://s3.amazonaws.com
S3_ACCESS_KEY_ID = os.getenv("S3_ACCESS_KEY_ID")
S3_SECRET_ACCESS_KEY = os.getenv("S3_SECRET_ACCESS_KEY")
S3_REGION = os.getenv("S3_REGION", "auto") # Optional: defaults to 'auto', use specific region if needed

# S3 bucket size limit (Default: 10GB)
S3_SIZE_LIMIT_GB = float(os.getenv("S3_SIZE_LIMIT_GB", 10.0))
S3_SIZE_LIMIT_BYTES = S3_SIZE_LIMIT_GB * 1024 * 1024 * 1024

# Enable S3 only if essential credentials and endpoint are provided
s3_vars_exist = all([
    S3_BUCKET,
    S3_ENDPOINT_URL,
    S3_ACCESS_KEY_ID,
    S3_SECRET_ACCESS_KEY
])
S3_ENABLED = s3_vars_exist


def format_size(size_bytes):
    """Format bytes to human readable format (KB, MB, GB, etc.)"""
    if size_bytes == 0:
        return "0B"
    size_names = ("B", "KB", "MB", "GB", "TB")
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024
        i += 1
    return f"{size_bytes:.2f} {size_names[i]}"

def backup_volume(volume_name, backup_dir, timestamp):
    """Backup a Docker volume to the specified directory."""
    try:
        # Create backup directory if it doesn't exist
        os.makedirs(backup_dir, exist_ok=True)
        
        # Generate backup file name
        backup_file = os.path.join(backup_dir, f"{volume_name}.tar")
        
        print(f"Backing up volume '{volume_name}' to {backup_file}...")
        
        # Run docker command to create backup
        cmd = f"docker run --rm -v {volume_name}:/volume -v {os.path.abspath(backup_dir)}:/backup alpine tar -cf /backup/{volume_name}.tar -C /volume ."
        subprocess.run(cmd, shell=True, check=True)
        
        # Create a timestamp file in the volume using the same format as directory name
        timestamp_cmd = f'docker run --rm -v {volume_name}:/volume alpine /bin/sh -c "echo \\"Last backup: {timestamp}\\" > /volume/last_backup.txt"'
        subprocess.run(timestamp_cmd, shell=True, check=True)
        print(f"Added timestamp placeholder to volume '{volume_name}'")
        
        # Get and format the size of the backup file
        file_size = os.path.getsize(backup_file)
        formatted_size = format_size(file_size)
        
        print(f"Successfully backed up volume '{volume_name}' to {backup_file} (Size: {formatted_size})")
        return True, formatted_size, backup_file, file_size
    except subprocess.CalledProcessError as e:
        print(f"Error backing up volume '{volume_name}': {e}", file=sys.stderr)
        return False, None, None, 0
    except Exception as e:
        print(f"Unexpected error backing up volume '{volume_name}': {e}", file=sys.stderr)
        return False, None, None, 0

def get_s3_client():
    """Create and return an S3 client configured for a generic S3 service."""
    # Use specific region if provided and not 'auto', otherwise let boto3 infer or handle 'auto'
    region = S3_REGION if S3_REGION and S3_REGION.lower() != 'auto' else None

    return boto3.client(
        's3',
        endpoint_url=S3_ENDPOINT_URL,
        aws_access_key_id=S3_ACCESS_KEY_ID,
        aws_secret_access_key=S3_SECRET_ACCESS_KEY,
        region_name=region, # Pass None if region is 'auto' or not set
        config=boto3.session.Config(
            signature_version='s3v4',
            # Checksum settings might vary based on S3 provider, keep for now
            request_checksum_calculation='WHEN_REQUIRED',
            response_checksum_validation='WHEN_REQUIRED'
        )
    )

def get_s3_bucket_size(s3_client, bucket_name):
    """Calculate the total size of objects in the S3 bucket."""
    try:
        total_size = 0
        paginator = s3_client.get_paginator('list_objects_v2')
        
        for page in paginator.paginate(Bucket=bucket_name):
            if 'Contents' in page:
                for obj in page['Contents']:
                    total_size += obj['Size']
        
        return total_size
    except ClientError as e:
        print(f"Error getting S3 bucket size: {e}", file=sys.stderr)
        return 0

def get_s3_backup_dirs(s3_client, bucket_name):
    """Get all backup directories (prefixes) in the S3 bucket with their sizes and timestamps."""
    dirs = {}
    try:
        paginator = s3_client.get_paginator('list_objects_v2')
        for page in paginator.paginate(Bucket=bucket_name):
            if 'Contents' in page:
                for obj in page['Contents']:
                    key = obj['Key']
                    size = obj['Size']
                    parts = key.split('/')
                    # Consider only objects within a top-level directory (prefix)
                    if len(parts) > 1:
                        dir_name = parts[0]
                        # Initialize directory info if first time seeing this prefix
                        if dir_name not in dirs:
                            try:
                                # Attempt to parse timestamp from directory name
                                timestamp = datetime.datetime.strptime(dir_name, "%Y%m%d_%H%M%S")
                            except ValueError:
                                # Use a very old date for directories not matching the timestamp format
                                timestamp = datetime.datetime(1970, 1, 1)
                            dirs[dir_name] = {'size': 0, 'timestamp': timestamp}
                        # Add object size to the directory's total size
                        dirs[dir_name]['size'] += size
        return dirs
    except ClientError as e:
        print(f"Error getting S3 backup directories: {e}", file=sys.stderr)
        return {}

def delete_s3_directory(s3_client, bucket_name, directory):
    """Delete all objects in a directory from the S3 bucket."""
    try:
        # List all objects in the directory
        paginator = s3_client.get_paginator('list_objects_v2')
        objects_to_delete = []
        
        for page in paginator.paginate(Bucket=bucket_name, Prefix=f"{directory}/"):
            if 'Contents' in page:
                for obj in page['Contents']:
                    objects_to_delete.append({'Key': obj['Key']})
        
        if objects_to_delete:
            # Delete the objects
            s3_client.delete_objects(
                Bucket=bucket_name,
                Delete={'Objects': objects_to_delete}
            )
            print(f"Deleted directory '{directory}' from S3 bucket")
            return True
        else:
            print(f"No objects found in directory '{directory}'")
            return False
    except ClientError as e:
        print(f"Error deleting directory '{directory}' from S3: {e}", file=sys.stderr)
        return False

def rotate_s3_backups(s3_client, bucket_name, required_space):
    """Delete oldest backup directories until enough space is freed."""
    try:
        # Get current bucket size
        current_size = get_s3_bucket_size(s3_client, bucket_name)
        target_size = S3_SIZE_LIMIT_BYTES - required_space

        # If we already have enough space, no need to rotate
        if current_size <= target_size:
            return True
        
        print(f"Need to free up at least {format_size(current_size - target_size)} to stay under the {format_size(S3_SIZE_LIMIT_BYTES)} limit")

        # Get all backup directories with their sizes and timestamps
        backup_dirs = get_s3_backup_dirs(s3_client, bucket_name)

        # Sort directories by timestamp (oldest first)
        sorted_dirs = sorted(
            [(dir_name, info['size'], info['timestamp']) for dir_name, info in backup_dirs.items()],
            key=lambda x: x[2]
        )
        
        # Delete oldest directories until we have enough space
        freed_space = 0
        deleted_dirs = []
        
        for dir_name, dir_size, timestamp in sorted_dirs:
            if current_size - freed_space <= target_size:
                break

            if delete_s3_directory(s3_client, bucket_name, dir_name):
                freed_space += dir_size
                deleted_dirs.append((dir_name, format_size(dir_size)))
        if deleted_dirs:
            print(f"Deleted {len(deleted_dirs)} old backup directories to free up space:")
            for dir_name, size in deleted_dirs:
                print(f"  - {dir_name} ({size})")
            print(f"Total freed space: {format_size(freed_space)}")
            return True
        else:
            print("Could not free up enough space by deleting old backups")
            return False
    except Exception as e:
        print(f"Error rotating S3 backups: {e}", file=sys.stderr) # Corrected print statement
        return False

def upload_to_s3(s3_client, bucket_name, file_path, file_size, timestamp_prefix): # Renamed function
    """Upload a file to the S3 bucket if it doesn't exceed the size limit.""" # Updated docstring
    try:
        # Get current bucket size
        current_bucket_size = get_s3_bucket_size(s3_client, bucket_name) # Use generic function

        # Check if adding this file would exceed the limit
        if current_bucket_size + file_size > S3_SIZE_LIMIT_BYTES: # Use generic limit
            print(f"Warning: Uploading this file would exceed the {format_size(S3_SIZE_LIMIT_BYTES)} S3 size limit.") # Generic print
            print(f"Current bucket size: {format_size(current_bucket_size)}")
            print(f"File size: {format_size(file_size)}")
            print(f"Combined size: {format_size(current_bucket_size + file_size)}")
            print(f"Limit: {format_size(S3_SIZE_LIMIT_BYTES)}") # Generic print

            # Try to rotate backups to make space
            if not rotate_s3_backups(s3_client, bucket_name, file_size): # Use generic function
                return False, "Could not free up enough space for backup"

            # Recheck bucket size after rotation
            current_bucket_size = get_s3_bucket_size(s3_client, bucket_name) # Use generic function
            if current_bucket_size + file_size > S3_SIZE_LIMIT_BYTES: # Use generic limit
                return False, f"Still exceeds {format_size(S3_SIZE_LIMIT_BYTES)} limit after rotation" # Generic print

        # Upload the file with timestamp prefix
        file_name = os.path.basename(file_path)
        s3_key = f"{timestamp_prefix}/{file_name}" # Use generic key name
        print(f"Uploading {file_name} to S3 bucket '{bucket_name}' as {s3_key}...") # Generic print
        s3_client.upload_file(file_path, bucket_name, s3_key)

        new_bucket_size = current_bucket_size + file_size
        print(f"Successfully uploaded to S3. Bucket usage: {format_size(new_bucket_size)} / {format_size(S3_SIZE_LIMIT_BYTES)}") # Generic print
        return True, None
    except ClientError as e:
        error_message = str(e)
        print(f"Error uploading to S3: {error_message}", file=sys.stderr) # Generic print
        return False, error_message

def check_volumes_exist(volume_names):
    """Check if any of the specified Docker volumes already exist."""
    existing_volumes = []
    for volume_name in volume_names:
        try:
            # Use 'docker volume inspect' which returns non-zero if volume doesn't exist
            subprocess.run(f"docker volume inspect {volume_name}", shell=True, check=True, capture_output=True)
            existing_volumes.append(volume_name)
        except subprocess.CalledProcessError:
            # Volume does not exist, continue checking others
            pass
        except Exception as e:
            print(f"Unexpected error checking volume '{volume_name}': {e}", file=sys.stderr)
            # Treat unexpected errors as potentially existing to be safe
            existing_volumes.append(volume_name)
    return existing_volumes

def restore_latest_backup(s3_client, bucket_name, target_volumes):
    """Finds the latest backup in S3, downloads it, and restores volumes."""
    print("Attempting to restore from the latest S3 backup...")

    # 1. Find the latest backup directory
    backup_dirs = get_s3_backup_dirs(s3_client, bucket_name)
    if not backup_dirs:
        print("Error: No backup directories found in the S3 bucket.", file=sys.stderr)
        return False

    # Sort by timestamp (latest first)
    sorted_dirs = sorted(
        [(dir_name, info['timestamp']) for dir_name, info in backup_dirs.items()],
        key=lambda x: x[1],
        reverse=True
    )
    latest_backup_dir_name = sorted_dirs[0][0]
    print(f"Found latest backup directory: {latest_backup_dir_name}")

    # 2. Create a temporary directory for downloads
    restore_temp_dir = os.path.join(DEFAULT_BACKUP_DIR, f"restore_{latest_backup_dir_name}")
    try:
        os.makedirs(restore_temp_dir, exist_ok=True)
        print(f"Created temporary download directory: {restore_temp_dir}")

        # 3. Download all .tar files from the latest backup directory
        paginator = s3_client.get_paginator('list_objects_v2')
        downloaded_files = []
        for page in paginator.paginate(Bucket=bucket_name, Prefix=f"{latest_backup_dir_name}/"):
            if 'Contents' in page:
                for obj in page['Contents']:
                    key = obj['Key']
                    if key.endswith('.tar'):
                        file_name = os.path.basename(key)
                        download_path = os.path.join(restore_temp_dir, file_name)
                        print(f"Downloading {key} to {download_path}...")
                        try:
                            s3_client.download_file(bucket_name, key, download_path)
                            downloaded_files.append(download_path)
                        except ClientError as e:
                            print(f"Error downloading {key}: {e}", file=sys.stderr)
                            return False # Stop restore if any download fails

        if not downloaded_files:
            print(f"Error: No .tar files found in the backup directory '{latest_backup_dir_name}'.", file=sys.stderr)
            return False

        # 4. Restore each volume from the downloaded tar files
        restored_count = 0
        for backup_file in downloaded_files:
            volume_name = os.path.basename(backup_file).replace('.tar', '')
            if volume_name in target_volumes:
                print(f"Restoring volume '{volume_name}' from {backup_file}...")
                try:
                    # Create the volume first
                    subprocess.run(f"docker volume create {volume_name}", shell=True, check=True, capture_output=True)
                    print(f"  Created Docker volume '{volume_name}'.")

                    # Extract the tarball into the volume
                    restore_cmd = f"docker run --rm -v {volume_name}:/volume -v {os.path.abspath(restore_temp_dir)}:/backup alpine tar -xf /backup/{volume_name}.tar -C /volume"
                    subprocess.run(restore_cmd, shell=True, check=True, capture_output=True)
                    print(f"  Successfully restored data to volume '{volume_name}'.")
                    restored_count += 1
                except subprocess.CalledProcessError as e:
                    print(f"  Error restoring volume '{volume_name}': {e.stderr.decode()}", file=sys.stderr)
                    # Continue trying to restore other volumes even if one fails
                except Exception as e:
                    print(f"  Unexpected error restoring volume '{volume_name}': {e}", file=sys.stderr)
            else:
                print(f"  Skipping restore for {volume_name} as it's not in the target list: {', '.join(target_volumes)}")

        print(f"\nRestore process finished. Successfully restored {restored_count} out of {len(target_volumes)} target volumes.")
        return restored_count == len(target_volumes) # Return True only if all target volumes were restored

    finally:
        # 5. Clean up the temporary directory
        if os.path.exists(restore_temp_dir):
            print(f"Cleaning up temporary directory: {restore_temp_dir}")
            shutil.rmtree(restore_temp_dir)

def main():
    parser = argparse.ArgumentParser(description="Backup or restore Docker volumes using S3.")
    parser.add_argument("--volumes", "-v", nargs="+", help="List of volume names to backup/restore (overrides BACKUP_VOLUMES env var).")
    parser.add_argument("--restore-latest", action="store_true", help="Restore volumes from the latest S3 backup instead of performing a backup.")
    args = parser.parse_args()

    # Determine volumes to process
    volumes = []
    if args.volumes:
        volumes = args.volumes
        print(f"Using volumes specified via --volumes argument: {', '.join(volumes)}")
    else:
        volumes_env = os.getenv(BACKUP_VOLUMES_ENV_VAR)
        if volumes_env:
            volumes = [vol.strip() for vol in volumes_env.split(',') if vol.strip()]
            print(f"Using volumes specified via {BACKUP_VOLUMES_ENV_VAR} environment variable: {', '.join(volumes)}")

    if not volumes:
        print(f"Error: No volumes specified. Please provide volumes via the --volumes argument or the {BACKUP_VOLUMES_ENV_VAR} environment variable (comma-separated).", file=sys.stderr)
        return 1

    base_backup_dir = DEFAULT_BACKUP_DIR

    # Create timestamped directory for this backup session
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_dir = os.path.join(base_backup_dir, timestamp)

    # Check if S3 is enabled based on environment variables
    s3_enabled = S3_ENABLED
    s3_client = None

    if s3_enabled:
        try:
            s3_client = get_s3_client()
            # Test connection by getting bucket size
            bucket_size = get_s3_bucket_size(s3_client, S3_BUCKET)
            print(f"Connected to S3 bucket '{S3_BUCKET}' at endpoint '{S3_ENDPOINT_URL}'.")
            print(f"Current usage: {format_size(bucket_size)} / {format_size(S3_SIZE_LIMIT_BYTES)}")
        except Exception as e:
            print(f"Error connecting to S3 service: {e}", file=sys.stderr)
            # If restore was requested, S3 connection failure is fatal
            if args.restore_latest:
                print("Cannot proceed with restore without S3 connection.", file=sys.stderr)
                return 1
            else:
                print("Continuing with local backups only.")
                s3_enabled = False # Disable S3 for backup if connection failed

    # --- Restore Logic ---
    if args.restore_latest:
        if not s3_enabled or not s3_client:
            print("Error: S3 is not configured or enabled. Cannot perform restore.", file=sys.stderr)
            print("Please ensure S3_BUCKET_NAME, S3_ENDPOINT_URL, S3_ACCESS_KEY_ID, and S3_SECRET_ACCESS_KEY are set in your .env file.")
            return 1

        # Check if target volumes already exist
        existing = check_volumes_exist(volumes)
        if existing:
            print("Error: Cannot restore because the following target volumes already exist:", file=sys.stderr)
            for vol in existing:
                print(f"  - {vol}", file=sys.stderr)
            print("Please remove or rename these volumes before attempting to restore.", file=sys.stderr)
            return 1

        # Proceed with restore
        restore_success = restore_latest_backup(s3_client, S3_BUCKET, volumes)
        return 0 if restore_success else 1

    # --- Backup Logic (if --restore-latest is not used) ---
    print(f"Starting backup of {len(volumes)} volumes to {backup_dir}")
    if s3_enabled:
        print(f"Backups will also be uploaded to S3 bucket '{S3_BUCKET}' in folder '{timestamp}'")

    # Track success/failure
    success_count = 0
    failed_volumes = []
    # Removed backup_details and s3_upload_details lists

    # Backup each volume
    for volume in volumes:
        print(f"\n--- Processing volume: {volume} ---")
        success, size_str, backup_file, file_size = backup_volume(volume, backup_dir, timestamp)
        if success:
            success_count += 1
            print(f"  Local backup successful: {volume} ({size_str})")

            # Upload to S3 if enabled
            if s3_enabled and s3_client and backup_file:
                s3_success, error_msg = upload_to_s3(s3_client, S3_BUCKET, backup_file, file_size, timestamp)
                if s3_success:
                    print(f"  S3 upload successful: {volume}")
                else:
                    print(f"  S3 upload FAILED: {volume} - {error_msg}")
                    # if S3 failure should mark the volume as failed overall
                    failed_volumes.append(f"{volume} (S3 Upload Failed)")
        else:
            failed_volumes.append(volume)
            print(f"  Local backup FAILED: {volume}")

    # Print summary
    print("\n----- Backup Summary -----")
    print(f"Timestamp: {timestamp}")
    print(f"Backup location: {backup_dir}")
    print(f"Total volumes processed: {len(volumes)}")
    print(f"Successful local backups: {success_count}")
    print(f"Failed operations: {len(failed_volumes)}")
    if failed_volumes:
        print("Failed volumes/operations:")
        for item in failed_volumes:
            print(f"  - {item}")
    print("--------------------------")

    return 0 if not failed_volumes else 1

if __name__ == "__main__":
    sys.exit(main())
