# Zurg configuration version
zurg: v1

# Provide your Real-Debrid API token
token: ${ZURG_RD_TOKEN} # https://real-debrid.com/apitoken

# Host and port settings
host: "[::]"
port: 9999

# Authentication (if required)
# username:
# password:

# Proxy settings (if needed)
proxy: "${ZURG_PROXY}"
concurrent_workers: 64
check_for_changes_every_secs: 10

# File handling and renaming settings
retain_rd_torrent_name: true
retain_folder_name_extension: true
expose_full_path: true

cache_network_test_results: true
auto_analyze_new_torrents: true

# Torrent management settings
enable_repair: true
rar_action: none

# Streaming and download link verification settings
disable_stream_proxy: false
verify_download_link: false

# sleep time after getting a 429 from Real-Debrid API
rate_limit_sleep_secs: 6

# time to wait before timing out
realdebrid_timeout_secs: 60

# api response failures until considered failed
retries_until_failed: 5

# Network and API settings
force_ipv6: false
network_buffer_size: 67108864 # 64MB
# Library update script configuration
# on_library_update: sh zurg_update_hook.sh "$@"
plex_server_url: "http://plex:32400"
plex_token: "${ZURG_PLEX_TOKEN}"
mount_path: "/mnt/zurg/"
# Directories configuration with specific filters for anime, shows, and movies
directories:
  shows:
    group_order: 20
    group: media
    filters:
      - has_episodes: true

  movies:
    group_order: 30
    group: media
    only_show_the_biggest_file: true
    filters:
      - regex: /.*/
