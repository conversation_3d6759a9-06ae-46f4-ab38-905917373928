# Debrid Media Server

Media server stack using Real-Debrid.

## Deployment 

0. Run the backup script on the existing deployment to create a backup of the volumes.
    ```
    python scripts/volume-backup.py
    ```
1. Add ssh key to github and checkout the repo.
2. Login to github registry (for zurg) using: Hardcoding the password since only my account has access to the zurg repo.
    ```
     echo "****************************************" | docker login ghcr.io djraval --password-stdin
    ```
3. Create a new venv(if needed) and install dependencies.
    ```
    uv venv
    source .venv/bin/activate
    uv pip install -r scripts/requirements.txt
    ```
4. Recreate the volumes using the latest backup from s3.
    ```
    python scripts/volume-backup.py --restore-latest
    ```
5. Run `docker-compose up -d` to start the services.

Ref: Good resource to setup cloudflare tunnel: https://mythofechelon.co.uk/blog/2024/1/7/how-to-set-up-free-secure-high-quality-remote-access-for-plex