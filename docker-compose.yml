services:
  zurg:
    image: ghcr.io/debridmediamanager/zurg:latest
    container_name: zurg
    restart: unless-stopped
    environment:
      TZ: America/Toronto
      LOG_LEVEL: ${ZURG_LOG_LEVEL}
    ports:
      - "127.0.0.1:9999:9999"
    volumes:
      - ./configs/zurg.yml:/app/config.yml
      - zurg_data:/app/data
      - /mnt/zurg:/mnt/zurg:rshared
    depends_on:
      warp:
        condition: service_healthy

  warp:
    image: caomingjun/warp
    container_name: warp
    restart: always
    device_cgroup_rules:
      - 'c 10:200 rwm'
    ports:
      - "127.0.0.1:1080:1080"
    environment:
      WARP_SLEEP: 2
      TZ: America/Toronto
    cap_add:
      - NET_ADMIN
    sysctls:
      - net.ipv6.conf.all.disable_ipv6=1
      - net.ipv4.conf.all.src_valid_mark=1
    volumes:
      - warp_data:/var/lib/cloudflare-warp

  rclone:
    image: rclone/rclone:latest
    container_name: rclone
    restart: unless-stopped
    environment:
      TZ: America/Toronto
    volumes:
      - /mnt/zurg:/mnt/zurg:rshared
      - ./configs/rclone.conf:/config/rclone/rclone.conf
    cap_add:
      - SYS_ADMIN
    security_opt:
      - apparmor:unconfined
    devices:
      - /dev/fuse:/dev/fuse:rwm
    depends_on:
      zurg:
        condition: service_healthy
    command: >
      mount zurg: /mnt/zurg
      --allow-non-empty
      --allow-other
      --attr-timeout 10y
      --buffer-size 4M
      --dir-cache-time 120s
      --poll-interval 60s
      --vfs-cache-max-age 2M
      --vfs-cache-max-size 2G
      --vfs-cache-min-free-space 200M
      --vfs-cache-mode full
      --vfs-cache-poll-interval 30s
      --vfs-fast-fingerprint
      --vfs-read-ahead 4M
      --vfs-read-chunk-size 8M
      --vfs-read-chunk-size-limit 0
      --vfs-read-wait 5ms
      --vfs-refresh
      --vfs-disk-space-total-size 6G
    healthcheck:
      test: ["CMD-SHELL", "rclone ls zurg:/ >/dev/null 2>&1 || exit 1"]
      interval: 5s
      timeout: 5s
      retries: 3
      start_period: 10s

  plex:
    image: linuxserver/plex:latest
    privileged: true
    container_name: plex
    environment:
      TZ: America/Toronto
      VERSION: docker
    ports:
      - "127.0.0.1:32400:32400"
    volumes:
      - plex_config:/config
      - /mnt/zurg:/mnt/zurg:rshared
    restart: unless-stopped
    devices:
      - /dev/dri:/dev/dri
      - /dev/bus/usb:/dev/bus/usb
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:32400/identity && ls /mnt/zurg/movies >/dev/null 2>&1 && ls /mnt/zurg/shows >/dev/null 2>&1 || exit 1"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 15s
    depends_on:
      rclone:
        condition: service_healthy

  tautulli:
    image: linuxserver/tautulli:latest
    container_name: tautulli
    environment:
      TZ: America/Toronto
    volumes:
      - tautulli_config:/config
      - plex_config:/plex
    ports:
      - "127.0.0.1:8181:8181"
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8181/status"]
      interval: 5s
      timeout: 5s
      retries: 5
      start_period: 15s

  cloudflare-tunnel:
    image: cloudflare/cloudflared:latest
    container_name: cloudflare-tunnel
    restart: unless-stopped
    environment:
      TZ: America/Toronto
      TUNNEL_TOKEN: ${CLOUDFLARE_TUNNEL_TOKEN}
    command: tunnel run

volumes:
  plex_config:
    external: true
  tautulli_config:
    external: true
  zurg_data:
    external: true
  warp_data:
